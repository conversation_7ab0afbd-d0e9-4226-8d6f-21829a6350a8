package com.cdkit.modules.cm.performance.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.application.project.CostProjectPlanApplicationService;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanCalculateResponse;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanCalculateRequest;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanDTO;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanAddRequest;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanEditRequest;
import com.cdkit.modules.cm.api.project.IProjectPlanApi;
import org.springframework.util.StringUtils;
import jakarta.validation.Valid;
import org.springframework.web.servlet.ModelAndView;

// Excel导出相关
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 项目计划
 * @Author: sunhzh
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Tag(name="项目计划")
@RestController
@RequestMapping("/cm/costProjectPlan")
@RequiredArgsConstructor
@Slf4j
public class CostProjectPlanController implements IProjectPlanApi {

    private final CostProjectPlanApplicationService costProjectPlanApplicationService;

    /**
     * 分页查询项目计划列表
     *
     * @param planName 计划名称
     * @param projectName 项目名称
     * @param projectCode 项目编号
     * @param planCode 计划编号
     * @param projectPlanStatus 状态
     * @param planType 计划类型
     * @param center 中心
     * @param projectGroup 项目组
     * @param current 当前页
     * @param size 每页大小
     * @return 分页结果
     */
    @Operation(summary = "分页查询项目计划列表", description = "支持按计划名称、项目名称、项目编号等条件进行分页查询")
    @GetMapping("/list")
    public Result<IPage<CostProjectPlanDTO>> queryPageList(
            @Parameter(description = "计划名称") @RequestParam(required = false) String planName,
            @Parameter(description = "项目名称") @RequestParam(required = false) String projectName,
            @Parameter(description = "项目编号") @RequestParam(required = false) String projectCode,
            @Parameter(description = "计划编号") @RequestParam(required = false) String planCode,
            @Parameter(description = "状态") @RequestParam(required = false) String projectPlanStatus,
            @Parameter(description = "计划类型") @RequestParam(required = false) String planType,
            @Parameter(description = "中心") @RequestParam(required = false) String center,
            @Parameter(description = "项目组") @RequestParam(required = false) String projectGroup,
            @Parameter(description = "年度预算ID") @RequestParam(required = false) String annualBudgetId,
            @Parameter(description = "项目类型") @RequestParam(required = false) String projectType,
            @Parameter(description = "当前页", required = true) @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", required = true) @RequestParam(defaultValue = "10") Integer size) {

        log.info("开始分页查询项目计划列表，查询条件：planName={}, projectName={}, projectCode={}, annualBudgetId={}, projectType={}, current={}, size={}",
                planName, projectName, projectCode, annualBudgetId, projectType, current, size);

        try {
            // 构建查询条件
            CostProjectPlanDTO queryDTO = new CostProjectPlanDTO();
            queryDTO.setPlanName(planName);
            queryDTO.setProjectName(projectName);
            queryDTO.setProjectCode(projectCode);
            queryDTO.setPlanCode(planCode);
            queryDTO.setProjectPlanStatus(projectPlanStatus);
            queryDTO.setPlanType(planType);
            queryDTO.setCenter(center);
            queryDTO.setProjectGroup(projectGroup);
            // {{ Add - 添加新字段查询条件. }}
            queryDTO.setAnnualBudgetId(annualBudgetId);
            queryDTO.setProjectType(projectType);

            // 构建分页条件
            PageReq pageReq = new PageReq();
            pageReq.setCurrent(current.longValue());
            pageReq.setSize(size.longValue());

            // 执行查询
            PageRes<CostProjectPlanDTO> pageRes = costProjectPlanApplicationService.queryPageList(queryDTO, pageReq);

            log.info("项目计划列表查询完成，总记录数：{}, 当前页：{}, 每页大小：{}",
                    pageRes.getTotal(), pageRes.getCurrent(), pageRes.getSize());

            IPage<CostProjectPlanDTO> page = new Page<CostProjectPlanDTO>(current, size);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(pageRes.getRecords());
            }

            return Result.OK(page);

        } catch (Exception e) {
            log.error("分页查询项目计划列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

     /**
     * 新增项目计划
     *
     * @param request 新增请求
     * @return 项目计划ID
     */
     @Override
     @Operation(summary = "新增项目计划", description = "新增项目计划，包含所有子表数据")
     public Result<String> add(@Valid @RequestBody CostProjectPlanAddRequest request) {

         log.info("开始新增项目计划，计划名称: {}", request.getPlanName());

         try {
             if (request == null) {
                 return Result.error("请求参数不能为空");
             }

             String planId = costProjectPlanApplicationService.add(request);

             log.info("项目计划新增成功，计划名称: {}, ID: {}", request.getPlanName(), planId);

             return Result.OK("新增成功");

         } catch (Exception e) {
             log.error("新增项目计划失败，计划名称: {}", request != null ? request.getPlanName() : "null", e);
             return Result.error("新增失败：" + e.getMessage());
         }
     }

     /**
     * 编辑项目计划
     *
     * @param request 编辑请求
     * @return 项目计划ID
     */
     @Override
     @Operation(summary = "编辑项目计划", description = "编辑项目计划，包含所有子表数据")
     public Result<String> edit(@Valid @RequestBody CostProjectPlanEditRequest request) {

         log.info("开始编辑项目计划，ID: {}, 计划名称: {}", request.getId(), request.getPlanName());

         try {
             if (request == null) {
                 return Result.error("请求参数不能为空");
             }

             String planId = costProjectPlanApplicationService.edit(request);

             log.info("项目计划编辑成功，ID: {}, 计划名称: {}", request.getId(), request.getPlanName());

             return Result.OK("编辑成功");

         } catch (Exception e) {
             log.error("编辑项目计划失败，ID: {}, 计划名称: {}",
                     request != null ? request.getId() : "null",
                     request != null ? request.getPlanName() : "null", e);
             return Result.error("编辑失败：" + e.getMessage());
         }
     }

     /**
     * 删除项目计划
     *
     * @param id 项目计划ID
     * @return 删除结果
     */
     @Override
     @Operation(summary = "删除项目计划", description = "删除项目计划及其所有子表数据")
     @DeleteMapping("/delete")
     public Result<String> delete(@RequestParam(name = "id", required = true) String id) {

         log.info("开始删除项目计划，ID: {}", id);

         try {
             if (!StringUtils.hasText(id)) {
                 return Result.error("项目计划ID不能为空");
             }

             costProjectPlanApplicationService.delete(id);

             log.info("项目计划删除成功，ID: {}", id);

             return Result.OK("删除成功");

         } catch (Exception e) {
             log.error("删除项目计划失败，ID: {}", id, e);
             return Result.error("删除失败：" + e.getMessage());
         }
     }

     /**
     * 批量删除项目计划
     *
     * @param ids 项目计划ID列表，逗号分隔
     * @return 删除结果
     */
     @Override
     @Operation(summary = "批量删除项目计划", description = "批量删除项目计划及其所有子表数据")
     @DeleteMapping("/deleteBatch")
     public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {

         log.info("开始批量删除项目计划，IDs: {}", ids);

         try {
             if (!StringUtils.hasText(ids)) {
                 return Result.error("项目计划ID列表不能为空");
             }

             costProjectPlanApplicationService.deleteBatch(ids);

             log.info("项目计划批量删除成功，IDs: {}", ids);

             return Result.OK("批量删除成功");

         } catch (Exception e) {
             log.error("批量删除项目计划失败，IDs: {}", ids, e);
             return Result.error("批量删除失败：" + e.getMessage());
         }
     }

     /**
     * 根据ID查询项目计划详情
     *
     * @param id 项目计划ID
     * @return 项目计划详情
     */
    @Operation(summary = "根据ID查询项目计划详情", description = "根据ID查询项目计划详情，包含所有子表数据")
    @GetMapping("/queryById")
    public Result<CostProjectPlanDTO> queryById(
            @Parameter(description = "项目计划ID", required = true) @RequestParam String id) {

        log.info("开始查询项目计划详情，id: {}", id);

        try {
            if (!StringUtils.hasText(id)) {
                return Result.error("项目计划ID不能为空");
            }

            CostProjectPlanDTO result = costProjectPlanApplicationService.getById(id);

            log.info("项目计划详情查询完成，id: {}, planName: {}", id, result.getPlanName());

            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询项目计划详情失败，id: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 计算项目计划预算依据
     *
     * @param request 项目计划数据
     * @return 计算结果
     */
    @Operation(summary = "计算项目计划预算依据", description = "根据项目计划明细数据计算预算依据，包括成本总计、项目利润、利润率等")
    @PostMapping("/calculate")
    public Result<CostProjectPlanCalculateResponse> calculateBudgetBasis(
            @Parameter(description = "项目计划数据", required = true) @RequestBody CostProjectPlanCalculateRequest request) {

        log.info("开始计算项目计划预算依据，计划名称: {}", request.getPlanName());

        try {
            if (request == null) {
                return Result.error("请求参数不能为空");
            }

            CostProjectPlanCalculateResponse response = costProjectPlanApplicationService.calculateBudgetBasisByData(request);

            log.info("项目计划预算依据计算完成，计划名称: {}, 成本总计: {}, 项目利润: {}, 利润率: {}%",
                    request.getPlanName(), response.getCostTotal(), response.getProjectProfit(), response.getProfitMargin());

            return Result.OK(response);

        } catch (Exception e) {
            log.error("计算项目计划预算依据失败，计划名称: {}", request != null ? request.getPlanName() : "null", e);
            return Result.error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 计算已保存的项目计划预算依据
     *
     * @param planId 项目计划ID
     * @return 计算结果
     */
    @Operation(summary = "计算已保存项目计划预算依据", description = "计算已保存的项目计划预算依据，只计算不保存")
    @PostMapping("/calculate/{planId}")
    public Result<CostProjectPlanCalculateResponse> calculateBudgetBasis(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId) {

        log.info("开始计算已保存项目计划预算依据，planId: {}", planId);

        try {
            if (!StringUtils.hasText(planId)) {
                return Result.error("项目计划ID不能为空");
            }

            CostProjectPlanCalculateResponse response = costProjectPlanApplicationService.calculateBudgetBasis(planId);

            log.info("已保存项目计划预算依据计算完成，planId: {}, 成本总计: {}, 项目利润: {}, 利润率: {}%",
                    planId, response.getCostTotal(), response.getProjectProfit(), response.getProfitMargin());

            return Result.OK(response);

        } catch (Exception e) {
            log.error("计算已保存项目计划预算依据失败，planId: {}", planId, e);
            return Result.error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 检查项目计划是否可以提交审批
     *
     * @param planId 项目计划ID
     * @return 检查结果
     */
    @Operation(summary = "检查是否可以提交审批", description = "检查项目计划是否满足提交审批的条件")
    @GetMapping("/canSubmit/{planId}")
    public Result<Boolean> canSubmitForApproval(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId) {

        log.info("检查项目计划是否可以提交审批，planId: {}", planId);

        try {
            if (!StringUtils.hasText(planId)) {
                return Result.error("项目计划ID不能为空");
            }

            boolean canSubmit = costProjectPlanApplicationService.canSubmitForApproval(planId);

            log.info("项目计划提交审批检查完成，planId: {}, 可以提交: {}", planId, canSubmit);

            return Result.OK(canSubmit);

        } catch (Exception e) {
            log.error("检查项目计划提交审批失败，planId: {}", planId, e);
            return Result.error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 提交项目计划审批
     *
     * @param planId 项目计划ID
     * @param request HTTP请求对象，用于传递租户信息给工作流
     * @return 工作流实例ID
     */
    @Operation(summary = "提交项目计划审批", description = "提交项目计划到F2审核系统进行审批，会自动传递租户信息")
    @PostMapping("/submit/{planId}")
    public Result<String> submitForApproval(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId,
            HttpServletRequest request) {

        log.info("开始提交项目计划审批，planId: {}", planId);

        try {
            if (!StringUtils.hasText(planId)) {
                return Result.error("项目计划ID不能为空");
            }

            String wiid = costProjectPlanApplicationService.submitForApproval(planId, request);

            log.info("项目计划提交审批成功，planId: {}, wiid: {}", planId, wiid);

            return Result.OK(wiid, "提交审批成功，工作流实例ID：" + wiid);

        } catch (Exception e) {
            log.error("提交项目计划审批失败，planId: {}", planId, e);
            return Result.error("提交审批失败：" + e.getMessage());
        }
    }
    @Operation(summary = "计算导出原料明细", description = "计算导出原料明细")
    @Override
    public ModelAndView exportXls(CostProjectPlanCalculateRequest requestO) {
        try {
            log.info("开始导出原料明细数据，计划名称: {}", requestO != null ? requestO.getPlanName() : "未知");

            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname();
            }

            // Step.2 参数校验
            if (requestO == null) {
                log.warn("导出原料明细失败：请求参数为空");
                // 返回空数据的Excel
                return createEmptyExcel(userName);
            }

            // Step.3 调用计算服务获取原料明细汇总数据
            CostProjectPlanCalculateResponse calculateResponse = costProjectPlanApplicationService.calculateBudgetBasisByData(requestO);

            // Step.4 提取原料明细汇总数据
            List<CostProjectPlanCalculateResponse.MaterialSummaryDTO> materialSummaryList =
                calculateResponse.getMaterialSummaryList();

            // 确保导出列表不为null
            if (materialSummaryList == null) {
                materialSummaryList = new ArrayList<>();
            }

            // Step.5 使用Cdkit包的导出逻辑
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "原料明细汇总");
            mv.addObject(NormalExcelConstants.CLASS, CostProjectPlanCalculateResponse.MaterialSummaryDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS,
                new ExportParams("原料明细汇总数据", "导出人:" + userName, "原料明细汇总"));
            mv.addObject(NormalExcelConstants.DATA_LIST, materialSummaryList);

            log.info("导出原料明细Excel成功，共{}条数据", materialSummaryList.size());
            return mv;

        } catch (Exception e) {
            log.error("导出原料明细Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                String userName = sysUser != null ? sysUser.getRealname() : "系统用户";
                return createEmptyExcel(userName);
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }

    /**
     * 创建空的Excel导出
     */
    private ModelAndView createEmptyExcel(String userName) {
        ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "原料明细汇总");
        mv.addObject(NormalExcelConstants.CLASS, CostProjectPlanCalculateResponse.MaterialSummaryDTO.class);
        mv.addObject(NormalExcelConstants.PARAMS,
            new ExportParams("原料明细汇总数据", "导出人:" + userName, "原料明细汇总"));
        mv.addObject(NormalExcelConstants.DATA_LIST, new ArrayList<>());
        log.warn("导出异常，返回空Excel文件");
        return mv;
    }

    /**
     * 转月度计划视图
     * 根据已锁定的季度计划生成月度计划显示数据，不保存到数据库
     *
     * @param planId 季度计划ID
     * @return 月度计划视图数据
     */
    @Operation(summary = "转月度计划视图", description = "根据已锁定的季度计划生成月度计划显示数据，明细表计划数量=产品用量÷3")
    @PostMapping("/convertToMonthlyView/{planId}")
    public Result<CostProjectPlanDTO> convertToMonthlyView(
            @Parameter(description = "季度计划ID", required = true) @PathVariable String planId) {

        log.info("开始转换月度计划视图，季度计划ID: {}", planId);

        try {
            if (!StringUtils.hasText(planId)) {
                return Result.error("季度计划ID不能为空");
            }

            CostProjectPlanDTO monthlyViewData = costProjectPlanApplicationService.convertToMonthlyView(planId);

            log.info("转换月度计划视图成功，季度计划ID: {}, 月度计划名称: {}", planId, monthlyViewData.getPlanName());

            return Result.OK(monthlyViewData);

        } catch (Exception e) {
            log.error("转换月度计划视图失败，季度计划ID: {}", planId, e);
            return Result.error("转换失败：" + e.getMessage());
        }
    }




}
